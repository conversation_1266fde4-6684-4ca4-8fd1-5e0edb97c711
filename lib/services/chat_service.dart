import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';
import 'auth_service.dart';

class ChatService {
  static const String _chatStorageKey = 'support_chat_messages';
  static const String _chatIdStorageKey = 'support_chat_id';

  static Future<Map<String, dynamic>> getOrCreateSupportChat() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing chat ID or create new one
      String? chatId = prefs.getString(_chatIdStorageKey);

      // Get existing messages
      final messagesJson = prefs.getStringList(_chatStorageKey) ?? [];
      final messages = messagesJson.map((json) => ChatMessage.fromJson(jsonDecode(json))).toList();

      // Get agent name based on time of day or random assignment
      final agentName = _getAssignedAgent();

      return {
        'chatId': chatId,
        'messages': messages,
        'agentName': agentName,
      };
    } catch (e) {
      throw Exception('Failed to initialize chat: $e');
    }
  }

  static Future<void> saveChatMessage(ChatMessage message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList(_chatStorageKey) ?? [];
      
      messagesJson.add(jsonEncode(message.toJson()));
      
      // Keep only last 100 messages to avoid storage issues
      if (messagesJson.length > 100) {
        messagesJson.removeRange(0, messagesJson.length - 100);
      }
      
      await prefs.setStringList(_chatStorageKey, messagesJson);
    } catch (e) {
      throw Exception('Failed to save message: $e');
    }
  }

  static Future<void> clearChat() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_chatStorageKey);
      await prefs.remove(_chatIdStorageKey);
    } catch (e) {
      throw Exception('Failed to clear chat: $e');
    }
  }

  static Future<List<SupportTopic>> getSupportTopics() async {
    // In a real app, this would be an API call
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      SupportTopic(
        id: 'booking',
        title: 'Booking Issues',
        description: 'Help with creating, modifying, or canceling bookings',
        icon: 'calendar_today',
        estimatedWaitTime: '2-5 minutes',
      ),
      SupportTopic(
        id: 'payment',
        title: 'Payment & Billing',
        description: 'Questions about charges, refunds, and payment methods',
        icon: 'payment',
        estimatedWaitTime: '3-7 minutes',
      ),
      SupportTopic(
        id: 'account',
        title: 'Account & Profile',
        description: 'Login issues, profile updates, and account settings',
        icon: 'person',
        estimatedWaitTime: '1-3 minutes',
      ),
      SupportTopic(
        id: 'service',
        title: 'Service Quality',
        description: 'Issues with completed services or runner performance',
        icon: 'star',
        estimatedWaitTime: '5-10 minutes',
      ),
      SupportTopic(
        id: 'runner',
        title: 'Runner Support',
        description: 'Questions about earnings, documents, or availability',
        icon: 'work',
        estimatedWaitTime: '3-8 minutes',
      ),
      SupportTopic(
        id: 'technical',
        title: 'Technical Issues',
        description: 'App bugs, connectivity issues, or feature problems',
        icon: 'bug_report',
        estimatedWaitTime: '5-15 minutes',
      ),
      SupportTopic(
        id: 'safety',
        title: 'Safety & Security',
        description: 'Report safety concerns or security issues',
        icon: 'security',
        estimatedWaitTime: 'Immediate',
        isUrgent: true,
      ),
      SupportTopic(
        id: 'other',
        title: 'Other Questions',
        description: 'General inquiries or feedback',
        icon: 'help',
        estimatedWaitTime: '5-10 minutes',
      ),
    ];
  }

  static Future<List<FAQ>> getFAQs() async {
    // In a real app, this would be an API call
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      FAQ(
        id: 'booking-cancel',
        question: 'How do I cancel a booking?',
        answer: 'You can cancel a booking by going to "My Bookings", selecting the booking, and tapping "Cancel Booking". Cancellations made more than 24 hours in advance receive a full refund.',
        category: 'booking',
        helpfulCount: 245,
      ),
      FAQ(
        id: 'payment-methods',
        question: 'What payment methods do you accept?',
        answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and digital wallets like Apple Pay and Google Pay.',
        category: 'payment',
        helpfulCount: 189,
      ),
      FAQ(
        id: 'runner-late',
        question: 'What if my runner is late?',
        answer: 'Runners should arrive within the scheduled time window. If your runner is more than 15 minutes late, you\'ll receive automatic updates. Contact support if there are significant delays.',
        category: 'service',
        helpfulCount: 156,
      ),
      FAQ(
        id: 'change-password',
        question: 'How do I change my password?',
        answer: 'Go to Profile > Settings > Security > Change Password. You\'ll need to enter your current password and choose a new one.',
        category: 'account',
        helpfulCount: 134,
      ),
      FAQ(
        id: 'runner-earnings',
        question: 'How do runner earnings work?',
        answer: 'Runners earn the full service fee minus TaskRabbit\'s service charge (typically 15-20%). Earnings are available for withdrawal 24-48 hours after job completion.',
        category: 'runner',
        helpfulCount: 203,
      ),
      FAQ(
        id: 'service-guarantee',
        question: 'Is there a service guarantee?',
        answer: 'Yes! All services are covered by the TaskRabbit Guarantee. If you\'re not satisfied with the work, contact us within 24 hours for a resolution.',
        category: 'service',
        helpfulCount: 298,
      ),
    ];
  }

  static String _getAssignedAgent() {
    final hour = DateTime.now().hour;
    final agents = [
      'Alex Thompson',
      'Sarah Wilson',
      'Mike Rodriguez', 
      'Emma Davis',
      'James Chen',
      'Lisa Johnson',
    ];
    
    // Assign agent based on time to simulate different shifts
    if (hour >= 9 && hour < 13) {
      return agents[0]; // Morning shift
    } else if (hour >= 13 && hour < 17) {
      return agents[1]; // Afternoon shift
    } else if (hour >= 17 && hour < 21) {
      return agents[2]; // Evening shift
    } else {
      return agents[3]; // Night shift
    }
  }

  static Future<void> submitFeedback({
    required int rating,
    required String comment,
    String? chatId,
  }) async {
    try {
      // In a real app, this would send feedback to the server
      await Future.delayed(const Duration(seconds: 1));
      
      // Store feedback locally for demo
      final prefs = await SharedPreferences.getInstance();
      final feedback = {
        'rating': rating,
        'comment': comment,
        'chatId': chatId,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      final feedbackList = prefs.getStringList('support_feedback') ?? [];
      feedbackList.add(jsonEncode(feedback));
      await prefs.setStringList('support_feedback', feedbackList);
      
    } catch (e) {
      throw Exception('Failed to submit feedback: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> getChatHistory() async {
    try {
      // In a real app, this would fetch from server
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList(_chatStorageKey) ?? [];
      
      return messagesJson.map((json) => jsonDecode(json) as Map<String, dynamic>).toList();
    } catch (e) {
      throw Exception('Failed to get chat history: $e');
    }
  }
}

class SupportTopic {
  final String id;
  final String title;
  final String description;
  final String icon;
  final String estimatedWaitTime;
  final bool isUrgent;

  SupportTopic({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.estimatedWaitTime,
    this.isUrgent = false,
  });
}

class FAQ {
  final String id;
  final String question;
  final String answer;
  final String category;
  final int helpfulCount;

  FAQ({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
    required this.helpfulCount,
  });
}