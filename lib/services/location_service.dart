import 'dart:async';
import 'dart:math';
import '../models/location_data.dart';

class LocationService {
  static final Map<String, StreamController<LocationUpdate>> _trackingStreams = {};
  static final Map<String, Timer> _locationTimers = {};

  static Stream<LocationUpdate> trackRunnerLocation(String errandId) {
    // Return existing stream if already tracking
    if (_trackingStreams.containsKey(errandId)) {
      return _trackingStreams[errandId]!.stream;
    }

    // Create new stream for this errand
    final controller = StreamController<LocationUpdate>.broadcast();
    _trackingStreams[errandId] = controller;

    // Start mock location updates
    _startMockLocationUpdates(errandId, controller);

    return controller.stream;
  }

  static Future<LocationUpdate> getRunnerLocation(String errandId) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Return mock initial location
    return _generateMockLocation(errandId);
  }

  static void stopTracking(String errandId) {
    _locationTimers[errandId]?.cancel();
    _locationTimers.remove(errandId);
    
    _trackingStreams[errandId]?.close();
    _trackingStreams.remove(errandId);
  }

  static void _startMockLocationUpdates(String errandId, StreamController<LocationUpdate> controller) {
    // Simulate runner moving from initial position towards customer
    final random = Random();
    var latitude = 40.7505; // Start position
    var longitude = -73.9934;
    
    // Target (customer location)
    const targetLat = 40.7589;
    const targetLng = -73.9851;
    
    var step = 0;
    const maxSteps = 30; // Number of updates to reach customer
    
    final timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (step >= maxSteps) {
        // Reached customer, stop moving but continue sending updates
        final finalLocation = LocationUpdate(
          errandId: errandId,
          location: LocationData(
            latitude: targetLat,
            longitude: targetLng,
            address: '123 Main Street, New York, NY 10001',
          ),
          timestamp: DateTime.now(),
          speed: 0.0,
          heading: 0.0,
          accuracy: 5.0,
          isMoving: false,
        );
        
        if (!controller.isClosed) {
          controller.add(finalLocation);
        }
        return;
      }
      
      // Move towards target with some randomness
      final progress = step / maxSteps;
      latitude += (targetLat - 40.7505) * (0.1 + random.nextDouble() * 0.05);
      longitude += (targetLng - -73.9934) * (0.1 + random.nextDouble() * 0.05);
      
      // Add some random variation to simulate real movement
      latitude += (random.nextDouble() - 0.5) * 0.0001;
      longitude += (random.nextDouble() - 0.5) * 0.0001;
      
      final locationUpdate = LocationUpdate(
        errandId: errandId,
        location: LocationData(
          latitude: latitude,
          longitude: longitude,
          address: _generateAddress(latitude, longitude),
        ),
        timestamp: DateTime.now(),
        speed: 20.0 + random.nextDouble() * 15.0, // 20-35 km/h
        heading: _calculateHeading(latitude, longitude, targetLat, targetLng),
        accuracy: 3.0 + random.nextDouble() * 7.0, // 3-10m accuracy
        isMoving: true,
      );
      
      if (!controller.isClosed) {
        controller.add(locationUpdate);
      }
      
      step++;
    });
    
    _locationTimers[errandId] = timer;
  }

  static LocationUpdate _generateMockLocation(String errandId) {
    final random = Random();
    return LocationUpdate(
      errandId: errandId,
      location: LocationData(
        latitude: 40.7505 + (random.nextDouble() - 0.5) * 0.01,
        longitude: -73.9934 + (random.nextDouble() - 0.5) * 0.01,
        address: 'Starting location, New York, NY',
      ),
      timestamp: DateTime.now(),
      speed: 0.0,
      heading: 0.0,
      accuracy: 5.0,
      isMoving: false,
    );
  }

  static String _generateAddress(double lat, double lng) {
    // Simple mock address generation based on coordinates
    final streetNumber = (lat * 10000).round() % 999 + 1;
    final streets = ['Broadway', 'Fifth Ave', 'Park Ave', 'Madison Ave', 'Lexington Ave'];
    final street = streets[(lng * 10000).round() % streets.length];
    return '$streetNumber $street, New York, NY';
  }

  static double _calculateHeading(double fromLat, double fromLng, double toLat, double toLng) {
    final dLng = (toLng - fromLng) * (pi / 180);
    final lat1Rad = fromLat * (pi / 180);
    final lat2Rad = toLat * (pi / 180);
    
    final y = sin(dLng) * cos(lat2Rad);
    final x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(dLng);
    
    final heading = atan2(y, x) * (180 / pi);
    return (heading + 360) % 360; // Normalize to 0-360
  }

  // Permission handling
  static Future<bool> requestLocationPermission() async {
    try {
      // In a real app, use permission_handler plugin
      await Future.delayed(const Duration(seconds: 1));
      return true; // Mock granted
    } catch (e) {
      return false;
    }
  }

  static Future<bool> checkLocationPermission() async {
    // In a real app, check actual permissions
    return true; // Mock granted
  }

  static Future<bool> isLocationServiceEnabled() async {
    // In a real app, check if location services are enabled
    return true; // Mock enabled
  }

  // Geofencing
  static Future<void> setGeofence({
    required String errandId,
    required LocationData center,
    required double radiusInMeters,
  }) async {
    // In a real app, set up geofencing
    await Future.delayed(const Duration(milliseconds: 500));
  }

  static Future<void> removeGeofence(String errandId) async {
    // In a real app, remove geofencing
    await Future.delayed(const Duration(milliseconds: 300));
  }

  // Utility functions
  static double calculateDistance(LocationData from, LocationData to) {
    const double earthRadiusKm = 6371;
    final double lat1Rad = from.latitude * (pi / 180);
    final double lat2Rad = to.latitude * (pi / 180);
    final double deltaLatRad = (to.latitude - from.latitude) * (pi / 180);
    final double deltaLngRad = (to.longitude - from.longitude) * (pi / 180);

    final double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    
    final double c = 2 * asin(sqrt(a));
    return earthRadiusKm * c;
  }

  static Future<String> getAddressFromCoordinates(double lat, double lng) async {
    // In a real app, use reverse geocoding
    await Future.delayed(const Duration(milliseconds: 800));
    return _generateAddress(lat, lng);
  }

  static Future<LocationData?> getCoordinatesFromAddress(String address) async {
    // In a real app, use geocoding
    await Future.delayed(const Duration(seconds: 1));
    
    // Mock geocoding
    return LocationData(
      latitude: 40.7589,
      longitude: -73.9851,
      address: address,
    );
  }

  // Route calculation
  static Future<RouteInfo> calculateRoute({
    required LocationData from,
    required LocationData to,
  }) async {
    // In a real app, use a routing service like Google Maps
    await Future.delayed(const Duration(seconds: 2));
    
    final distance = calculateDistance(from, to);
    final estimatedTime = (distance / 30) * 60; // Assume 30 km/h average speed
    
    return RouteInfo(
      distance: distance,
      estimatedTimeMinutes: estimatedTime.round(),
      polylinePoints: _generateMockPolyline(from, to),
    );
  }

  static List<LocationData> _generateMockPolyline(LocationData from, LocationData to) {
    final points = <LocationData>[];
    const steps = 10;
    
    for (int i = 0; i <= steps; i++) {
      final ratio = i / steps;
      final lat = from.latitude + (to.latitude - from.latitude) * ratio;
      final lng = from.longitude + (to.longitude - from.longitude) * ratio;
      
      points.add(LocationData(
        latitude: lat,
        longitude: lng,
        address: '',
      ));
    }
    
    return points;
  }

  static void dispose() {
    for (final timer in _locationTimers.values) {
      timer.cancel();
    }
    _locationTimers.clear();
    
    for (final controller in _trackingStreams.values) {
      controller.close();
    }
    _trackingStreams.clear();
  }
}

class RouteInfo {
  final double distance; // in kilometers
  final int estimatedTimeMinutes;
  final List<LocationData> polylinePoints;

  RouteInfo({
    required this.distance,
    required this.estimatedTimeMinutes,
    required this.polylinePoints,
  });

  String get formattedDistance {
    if (distance < 1) {
      return '${(distance * 1000).round()} m';
    }
    return '${distance.toStringAsFixed(1)} km';
  }

  String get formattedTime {
    if (estimatedTimeMinutes < 60) {
      return '$estimatedTimeMinutes min';
    }
    final hours = estimatedTimeMinutes ~/ 60;
    final minutes = estimatedTimeMinutes % 60;
    return '${hours}h ${minutes}m';
  }
}