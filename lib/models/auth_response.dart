import 'user.dart';

class AuthResponse {
  final String? token;
  final User? user;
  final String message;

  AuthResponse({
    this.token,
    this.user,
    required this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      user: json['user'] != null ? User.from<PERSON>son(json['user']) : null,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'user': user?.toJson(),
      'message': message,
    };
  }
}

class LoginRequest {
  final String email;
  final String password;

  LoginRequest({
    required this.email,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

class RegisterRequest {
  final String name;
  final String email;
  final String password;
  final String passwordConfirmation;
  final UserRole role;
  final String? phoneNumber;

  RegisterRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.passwordConfirmation,
    required this.role,
    this.phoneNumber,
  });

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'name': name,
      'email': email,
      'password': password,
      'password_confirmation': passwordConfirmation,
      'role': role.toString().split('.').last,
      if (phoneNumber != null) 'phone_number': phoneNumber,
    };
  }
}

class ForgotPasswordRequest {
  final String email;

  ForgotPasswordRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

class UpdateProfileRequest {
  final String? name;
  final String? phoneNumber;

  UpdateProfileRequest({
    this.name,
    this.phoneNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      if (name != null) 'name': name,
      if (phoneNumber != null) 'phone_number': phoneNumber,
    };
  }
}