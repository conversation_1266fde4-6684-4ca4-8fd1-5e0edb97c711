class AppNotification {
  final String id;
  final NotificationData data;
  final DateTime? readAt;
  final DateTime createdAt;

  AppNotification({
    required this.id,
    required this.data,
    this.readAt,
    required this.createdAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'],
      data: NotificationData.fromJson(json['data']),
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'data': data.toJson(),
      'read_at': readAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isRead => readAt != null;
}

class NotificationData {
  final String message;
  final String? title;
  final NotificationType type;
  final Map<String, dynamic>? payload;

  NotificationData({
    required this.message,
    this.title,
    required this.type,
    this.payload,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      message: json['message'],
      title: json['title'],
      type: NotificationType.values.firstWhere(
        (type) => type.toString().split('.').last == json['type'],
        orElse: () => NotificationType.general,
      ),
      payload: json['payload'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'title': title,
      'type': type.toString().split('.').last,
      'payload': payload,
    };
  }
}

enum NotificationType {
  general,
  booking,
  errand,
  payment,
  system,
}

class DeviceTokenRequest {
  final String token;

  DeviceTokenRequest({required this.token});

  Map<String, dynamic> toJson() {
    return {
      'token': token,
    };
  }
}