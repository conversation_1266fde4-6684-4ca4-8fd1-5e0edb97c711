class ChatMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String? senderAvatar;
  final String content;
  final DateTime timestamp;
  final MessageType type;
  final bool isFromSupport;
  final String? attachmentUrl;
  final String? attachmentType;
  final MessageStatus status;

  ChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.content,
    required this.timestamp,
    required this.type,
    required this.isFromSupport,
    this.attachmentUrl,
    this.attachmentType,
    this.status = MessageStatus.sent,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      senderId: json['senderId'],
      senderName: json['senderName'],
      senderAvatar: json['senderAvatar'],
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
      type: MessageType.values.firstWhere(
        (type) => type.toString().split('.').last == json['type'],
        orElse: () => MessageType.text,
      ),
      isFromSupport: json['isFromSupport'] ?? false,
      attachmentUrl: json['attachmentUrl'],
      attachmentType: json['attachmentType'],
      status: MessageStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => MessageStatus.sent,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'type': type.toString().split('.').last,
      'isFromSupport': isFromSupport,
      'attachmentUrl': attachmentUrl,
      'attachmentType': attachmentType,
      'status': status.toString().split('.').last,
    };
  }

  ChatMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? content,
    DateTime? timestamp,
    MessageType? type,
    bool? isFromSupport,
    String? attachmentUrl,
    String? attachmentType,
    MessageStatus? status,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      isFromSupport: isFromSupport ?? this.isFromSupport,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      attachmentType: attachmentType ?? this.attachmentType,
      status: status ?? this.status,
    );
  }

  bool get isText => type == MessageType.text;
  bool get isImage => type == MessageType.image;
  bool get isFile => type == MessageType.file;
  bool get hasAttachment => attachmentUrl != null;
}

enum MessageType {
  text,
  image,
  file,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

class ChatSession {
  final String id;
  final String userId;
  final String? agentId;
  final String? agentName;
  final ChatStatus status;
  final DateTime createdAt;
  final DateTime? endedAt;
  final String? topic;
  final int? rating;
  final String? feedback;
  final List<ChatMessage> messages;

  ChatSession({
    required this.id,
    required this.userId,
    this.agentId,
    this.agentName,
    required this.status,
    required this.createdAt,
    this.endedAt,
    this.topic,
    this.rating,
    this.feedback,
    this.messages = const [],
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'],
      userId: json['userId'],
      agentId: json['agentId'],
      agentName: json['agentName'],
      status: ChatStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => ChatStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      endedAt: json['endedAt'] != null ? DateTime.parse(json['endedAt']) : null,
      topic: json['topic'],
      rating: json['rating'],
      feedback: json['feedback'],
      messages: (json['messages'] as List<dynamic>?)
          ?.map((msg) => ChatMessage.fromJson(msg))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'agentId': agentId,
      'agentName': agentName,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'endedAt': endedAt?.toIso8601String(),
      'topic': topic,
      'rating': rating,
      'feedback': feedback,
      'messages': messages.map((msg) => msg.toJson()).toList(),
    };
  }

  bool get isActive => status == ChatStatus.active;
  bool get isEnded => status == ChatStatus.ended;
  bool get hasRating => rating != null;
  Duration get duration {
    final end = endedAt ?? DateTime.now();
    return end.difference(createdAt);
  }
}

enum ChatStatus {
  active,
  waiting,
  ended,
  escalated,
}

class QuickReply {
  final String id;
  final String text;
  final String? category;

  QuickReply({
    required this.id,
    required this.text,
    this.category,
  });

  factory QuickReply.fromJson(Map<String, dynamic> json) {
    return QuickReply(
      id: json['id'],
      text: json['text'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'category': category,
    };
  }
}