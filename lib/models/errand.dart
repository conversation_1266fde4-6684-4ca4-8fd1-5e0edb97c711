class Errand {
  final int id;
  final String title;
  final String description;
  final double payment;
  final String location;
  final int? customerId;
  final int? runnerId;
  final String? runnerName;
  final String? runnerPhone;
  final ErrandStatus status;
  final DateTime scheduledDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? notes;

  // Additional computed properties for display
  String get serviceName => title;
  double get totalAmount => payment;

  Errand({
    required this.id,
    required this.title,
    required this.description,
    required this.payment,
    required this.location,
    this.customerId,
    this.runnerId,
    this.runnerName,
    this.runnerPhone,
    required this.status,
    required this.scheduledDate,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
  });

  factory Errand.fromJson(Map<String, dynamic> json) {
    return Errand(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      payment: json['payment'].toDouble(),
      location: json['location'] ?? '',
      customerId: json['customer_id'],
      runnerId: json['runner_id'],
      runnerName: json['runner_name'],
      runnerPhone: json['runner_phone'],
      status: ErrandStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => ErrandStatus.requested,
      ),
      scheduledDate: json['scheduled_at'] != null
          ? DateTime.parse(json['scheduled_at'])
          : DateTime.now(),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'payment': payment,
      'location': location,
      'customer_id': customerId,
      'runner_id': runnerId,
      'runner_name': runnerName,
      'runner_phone': runnerPhone,
      'status': status.toString().split('.').last,
      'scheduled_at': scheduledDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'notes': notes,
    };
  }
}

enum ErrandStatus {
  requested,
  accepted,
  inProgress,
  completed,
  cancelled,
}

class RunnerEarning {
  final int id;
  final double amount;
  final String description;
  final DateTime date;
  final EarningType type;

  RunnerEarning({
    required this.id,
    required this.amount,
    required this.description,
    required this.date,
    required this.type,
  });

  factory RunnerEarning.fromJson(Map<String, dynamic> json) {
    return RunnerEarning(
      id: json['id'],
      amount: json['amount'].toDouble(),
      description: json['description'],
      date: DateTime.parse(json['date']),
      type: EarningType.values.firstWhere(
        (type) => type.toString().split('.').last == json['type'],
        orElse: () => EarningType.job,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'description': description,
      'date': date.toIso8601String(),
      'type': type.toString().split('.').last,
    };
  }
}

enum EarningType {
  job,
  tip,
  bonus,
}

class WithdrawRequest {
  final double amount;
  final String method;

  WithdrawRequest({
    required this.amount,
    required this.method,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'method': method,
    };
  }
}