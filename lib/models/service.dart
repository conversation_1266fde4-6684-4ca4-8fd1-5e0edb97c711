class Service {
  final int id;
  final String name;
  final String description;
  final double? price;
  final String? category;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  Service({
    required this.id,
    required this.name,
    required this.description,
    this.price,
    this.category,
    this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price']?.toDouble(),
      category: json['category'],
      imageUrl: json['image_url'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'image_url': imageUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class Booking {
  final int id;
  final int serviceId;
  final int customerId;
  final int? runnerId;
  final DateTime bookingDate;
  final String address;
  final BookingStatus status;
  final double? amount;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Booking({
    required this.id,
    required this.serviceId,
    required this.customerId,
    this.runnerId,
    required this.bookingDate,
    required this.address,
    required this.status,
    this.amount,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'],
      serviceId: json['service_id'],
      customerId: json['customer_id'],
      runnerId: json['runner_id'],
      bookingDate: DateTime.parse(json['booking_date']),
      address: json['address'],
      status: BookingStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => BookingStatus.pending,
      ),
      amount: json['amount']?.toDouble(),
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'service_id': serviceId,
      'customer_id': customerId,
      'runner_id': runnerId,
      'booking_date': bookingDate.toIso8601String(),
      'address': address,
      'status': status.toString().split('.').last,
      'amount': amount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

enum BookingStatus {
  pending,
  accepted,
  started,
  completed,
  cancelled,
}

class CreateBookingRequest {
  final int serviceId;
  final DateTime bookingDate;
  final String address;
  final String? notes;

  CreateBookingRequest({
    required this.serviceId,
    required this.bookingDate,
    required this.address,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'booking_date': bookingDate.toIso8601String(),
      'address': address,
      if (notes != null) 'notes': notes,
    };
  }
}