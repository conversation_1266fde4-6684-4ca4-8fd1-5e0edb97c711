import 'package:flutter/material.dart';
import 'config/app_router.dart';
import 'config/firebase_config.dart';
import 'services/firebase_messaging_service.dart';
import 'services/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase for messaging and storage only
  await FirebaseConfig.initialize();
  
  // Initialize Firebase Messaging
  await FirebaseMessagingService.initialize();
  
  // Initialize Laravel Sanctum authentication
  await AuthService.initializeAuth();
  
  runApp(const TaskRabbitApp());
}

class TaskRabbitApp extends StatelessWidget {
  const TaskRabbitApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'TaskRabbit Inc',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF1B365D),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1B365D),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1B365D),
          foregroundColor: Colors.white,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1B365D),
            foregroundColor: Colors.white,
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF1B365D)),
          ),
        ),
        useMaterial3: true,
      ),
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
    );
  }
}
