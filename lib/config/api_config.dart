class ApiConfig {
  static const String baseUrl = 'http://192.168.10.182:8080';
  static const String apiVersion = 'v1';
  
  static String get baseApiUrl => '$baseUrl/api/$apiVersion';
  
  // Auth endpoints
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String forgotPasswordEndpoint = '/auth/forgot-password';
  
  // User endpoints
  static const String userProfileEndpoint = '/users/profile';
  static const String userProfilePhotoEndpoint = '/runner/profile/photo';
  
  // Service endpoints
  static const String servicesEndpoint = '/services';
  static const String bookingsEndpoint = '/bookings';
  
  // Runner endpoints
  static const String availableErrandsEndpoint = '/errands/available';
  static const String acceptErrandEndpoint = '/errands/{id}/accept';
  static const String runnerDocumentsEndpoint = '/runner/documents';
  static const String runnerDocumentsUploadEndpoint = '/runner/documents/upload';
  static const String runnerEarningsEndpoint = '/runners/earnings/history';
  static const String runnerWithdrawEndpoint = '/runners/earnings/withdraw';
  static const String runnerAvailabilityEndpoint = '/runners/availability';
  
  // Notification endpoints
  static const String notificationsEndpoint = '/notifications';
  static const String markNotificationReadEndpoint = '/notifications/{id}/read';
  static const String deviceTokenEndpoint = '/notifications/device';
  
  // Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };
}